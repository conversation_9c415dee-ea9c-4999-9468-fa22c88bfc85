/*
 * Copyright (C) 2016 mendhak
 *
 * This file is part of GPSLogger for Android.
 *
 * GPSLogger for Android is free software: you can redistribute it and/or modify
 * it under the terms of the GNU General Public License as published by
 * the Free Software Foundation, either version 2 of the License, or
 * (at your option) any later version.
 *
 * GPSLogger for Android is distributed in the hope that it will be useful,
 * but WITHOUT ANY WARRANTY; without even the implied warranty of
 * MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE.  See the
 * GNU General Public License for more details.
 *
 * You should have received a copy of the GNU General Public License
 * along with GPSLogger for Android.  If not, see <http://www.gnu.org/licenses/>.
 */

package com.mendhak.gpslogger.ui.fragments.settings;

import android.app.ActivityManager;
import android.content.Context;
import android.content.Intent;
import android.content.pm.PackageManager;
import android.net.Uri;
import android.os.Build;
import android.os.Bundle;
import android.widget.Toast;

import androidx.annotation.NonNull;
import androidx.preference.ListPreference;
import androidx.preference.Preference;
import androidx.preference.PreferenceFragmentCompat;
import androidx.preference.SwitchPreferenceCompat;
import com.mendhak.gpslogger.R;
import com.mendhak.gpslogger.common.PreferenceHelper;
import com.mendhak.gpslogger.common.PreferenceNames;
import com.mendhak.gpslogger.common.Strings;
import com.mendhak.gpslogger.common.events.AnnotationEvents;
import com.mendhak.gpslogger.common.network.ConscryptProviderInstaller;
import com.mendhak.gpslogger.common.slf4j.Logs;
import com.mendhak.gpslogger.loggers.Files;
import com.mendhak.gpslogger.ui.Dialogs;

import org.slf4j.Logger;

import java.io.File;
import java.util.*;

import de.greenrobot.event.EventBus;
import eltos.simpledialogfragment.SimpleDialog;


public class GeneralSettingsFragment extends PreferenceFragmentCompat implements
        SimpleDialog.OnDialogResultListener,
        Preference.OnPreferenceClickListener,
        Preference.OnPreferenceChangeListener {

    Logger LOG = Logs.of(GeneralSettingsFragment.class);
    private PreferenceHelper preferenceHelper = PreferenceHelper.getInstance();

    private static final int REQUEST_AUDIO_FILE = 1001;

    @Override
    public void onCreate(Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);

        findPreference("enableDisableGps").setOnPreferenceClickListener(this);
        findPreference("debuglogtoemail").setOnPreferenceClickListener(this);

        findPreference(PreferenceNames.APP_THEME_SETTING).setOnPreferenceChangeListener(this);
        findPreference(PreferenceNames.ANNOTATIONS_BUTTON_COUNT).setOnPreferenceChangeListener(this);
        findPreference(PreferenceNames.HAPTIC_FEEDBACK_ENABLED).setOnPreferenceChangeListener(this);
        findPreference(PreferenceNames.HAPTIC_FEEDBACK_INTENSITY).setOnPreferenceChangeListener(this);

        // Audio feedback settings
        findPreference(PreferenceNames.AUDIO_FEEDBACK_TYPE).setOnPreferenceChangeListener(this);
        findPreference(PreferenceNames.AUDIO_FEEDBACK_VOICE_ENABLED).setOnPreferenceChangeListener(this);
        findPreference(PreferenceNames.AUDIO_FEEDBACK_TEXT_ENABLED).setOnPreferenceChangeListener(this);
        findPreference(PreferenceNames.AUDIO_FEEDBACK_COUNTER_ENABLED).setOnPreferenceChangeListener(this);

        // TTS feedback settings
        findPreference(PreferenceNames.TTS_FEEDBACK_VOICE_ENABLED).setOnPreferenceChangeListener(this);
        findPreference(PreferenceNames.TTS_FEEDBACK_TEXT_ENABLED).setOnPreferenceChangeListener(this);
        findPreference(PreferenceNames.TTS_FEEDBACK_COUNTER_ENABLED).setOnPreferenceChangeListener(this);

        // Audio feedback click listeners
        findPreference("audio_feedback_custom_file").setOnPreferenceClickListener(this);
        findPreference("audio_feedback_test").setOnPreferenceClickListener(this);

        // Add listeners for new annotation layout settings
        findPreference(PreferenceNames.ANNOTATION_LAYOUT_STYLE).setOnPreferenceChangeListener(this);
        findPreference(PreferenceNames.ANNOTATION_SPACING_MODE).setOnPreferenceChangeListener(this);
        findPreference(PreferenceNames.ANNOTATION_CUSTOM_SPACING).setOnPreferenceChangeListener(this);
        findPreference(PreferenceNames.ANNOTATION_VIEW_MODE).setOnPreferenceChangeListener(this);

        // Voice input settings
        findPreference(PreferenceNames.VOICE_INPUT_ENABLED).setOnPreferenceChangeListener(this);
        findPreference(PreferenceNames.VOICE_INPUT_LANGUAGE).setOnPreferenceClickListener(this);
        findPreference(PreferenceNames.VOICE_INPUT_TIMEOUT).setOnPreferenceClickListener(this);

        setPreferenceVoiceInputSummaries();
        setAudioFeedbackSummaries();

        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.KITKAT) {
            findPreference("resetapp").setOnPreferenceClickListener(this);
        }
        else {
            findPreference("resetapp").setEnabled(false);
        }


        if(Build.VERSION.SDK_INT >= Build.VERSION_CODES.O){
            SwitchPreferenceCompat hideNotificiationPreference = findPreference("hide_notification_from_status_bar");
            hideNotificiationPreference.setEnabled(false);
            hideNotificiationPreference.setDefaultValue(false);
            hideNotificiationPreference.setChecked(false);
            hideNotificiationPreference.setSummary(getString(R.string.hide_notification_from_status_bar_disallowed));
        }

        setCoordinatesFormatPreferenceItem();
        setLanguagesPreferenceItem();
        Preference conscryptPreference = findPreference("install_conscrypt_provider");
        conscryptPreference.setEnabled(ConscryptProviderInstaller.shouldPromptUserForInstallation());
        conscryptPreference.setIntent(ConscryptProviderInstaller.getConscryptInstallationIntent(getActivity()));



        Preference aboutInfo = findPreference("about_version_info");
        try {

            aboutInfo.setTitle("GPSLogger version " + getActivity().getPackageManager().getPackageInfo(getActivity().getPackageName(), 0).versionName);
        } catch (PackageManager.NameNotFoundException e) {
        }
    }

    private void setPreferenceVoiceInputSummaries() {
        // Set voice input language summary
        String language = preferenceHelper.getVoiceInputLanguage();
        if (language.isEmpty()) {
            language = "系统默认";
        }
        findPreference(PreferenceNames.VOICE_INPUT_LANGUAGE).setSummary(language);

        // Set voice input timeout summary
        int timeout = preferenceHelper.getVoiceInputTimeout();
        findPreference(PreferenceNames.VOICE_INPUT_TIMEOUT).setSummary(timeout + " 秒");
    }

    private void setAudioFeedbackSummaries() {
        // Set custom audio file summary
        Preference customFilePref = findPreference("audio_feedback_custom_file");
        if (customFilePref != null) {
            String customPath = preferenceHelper.getString(PreferenceNames.AUDIO_FEEDBACK_CUSTOM_PATH, "");
            if (!customPath.isEmpty()) {
                try {
                    android.net.Uri uri = android.net.Uri.parse(customPath);
                    String fileName = getFileNameFromUri(uri);
                    customFilePref.setSummary("已选择: " + fileName);
                } catch (Exception e) {
                    customFilePref.setSummary("点击选择音频文件");
                }
            } else {
                customFilePref.setSummary("点击选择音频文件");
            }
        }
    }

    @Override
    public void onCreatePreferences(Bundle savedInstanceState, String rootKey) {
        setPreferencesFromResource(R.xml.pref_general, rootKey);
    }

    private void setLanguagesPreferenceItem() {
        ListPreference langs = findPreference("changelanguage");

        Map<String,String> localeDisplayNames = Strings.getAvailableLocales(getActivity());

        String[] locales = localeDisplayNames.keySet().toArray(new String[localeDisplayNames.keySet().size()]);
        String[] displayValues = localeDisplayNames.values().toArray(new String[localeDisplayNames.values().size()]);

        langs.setEntries(displayValues);
        langs.setEntryValues(locales);
        langs.setDefaultValue("en");
        langs.setOnPreferenceChangeListener(this);
    }

    private void setCoordinatesFormatPreferenceItem() {
        ListPreference coordFormats = findPreference("coordinatedisplayformat");
        String[] coordinateDisplaySamples = new String[]{"12° 34' 56.7890\" S","12° 34.5678' S","-12.345678"};
        coordFormats.setEntries(coordinateDisplaySamples);
        coordFormats.setEntryValues(new String[]{PreferenceNames.DegreesDisplayFormat.DEGREES_MINUTES_SECONDS.toString(),PreferenceNames.DegreesDisplayFormat.DEGREES_DECIMAL_MINUTES.toString(),PreferenceNames.DegreesDisplayFormat.DECIMAL_DEGREES.toString()});
        coordFormats.setDefaultValue("0");
        coordFormats.setOnPreferenceChangeListener(this);
        coordFormats.setSummary(coordinateDisplaySamples[PreferenceHelper.getInstance().getDisplayLatLongFormat().ordinal()]);
    }



    @Override
    public boolean onPreferenceClick(Preference preference) {

        if (preference.getKey().equals("enableDisableGps")) {
            startActivity(new Intent("android.settings.LOCATION_SOURCE_SETTINGS"));
            return true;
        }

        if(preference.getKey().equalsIgnoreCase(PreferenceNames.VOICE_INPUT_LANGUAGE)){
            eltos.simpledialogfragment.form.SimpleFormDialog.build()
                    .title("语音识别语言")
                    .msg("输入语音识别的语言代码（如zh-CN、en-US），留空使用系统默认")
                    .fields(
                            eltos.simpledialogfragment.form.Input.plain(PreferenceNames.VOICE_INPUT_LANGUAGE)
                                    .hint("语言代码")
                                    .text(preferenceHelper.getVoiceInputLanguage())
                    )
                    .pos(R.string.ok)
                    .neg(R.string.cancel)
                    .show(this, PreferenceNames.VOICE_INPUT_LANGUAGE);
            return true;
        }

        if(preference.getKey().equalsIgnoreCase(PreferenceNames.VOICE_INPUT_TIMEOUT)){
            eltos.simpledialogfragment.form.SimpleFormDialog.build()
                    .title("语音识别超时")
                    .msg("设置语音识别的超时时间（秒）")
                    .fields(
                            eltos.simpledialogfragment.form.Input.plain(PreferenceNames.VOICE_INPUT_TIMEOUT)
                                    .hint("超时时间")
                                    .inputType(android.text.InputType.TYPE_CLASS_NUMBER)
                                    .text(String.valueOf(preferenceHelper.getVoiceInputTimeout()))
                    )
                    .pos(R.string.ok)
                    .neg(R.string.cancel)
                    .show(this, PreferenceNames.VOICE_INPUT_TIMEOUT);
            return true;
        }

        if (preference.getKey().equals("resetapp")) {
            SimpleDialog.build()
                    .title(getString(R.string.reset_app_title))
                    .msgHtml(getString(R.string.reset_app_summary))
                    .neg(R.string.cancel)
                    .show(this, "RESET_APP");
            return true;
        }

        if(preference.getKey().equals("debuglogtoemail")){
            Intent intent = new Intent(Intent.ACTION_SEND);
            intent.setType("text/plain");
            intent.putExtra(Intent.EXTRA_SUBJECT, "GPSLogger Debug Log");

            StringBuilder diagnostics = new StringBuilder();
            diagnostics.append("Android version: ").append(Build.VERSION.SDK_INT).append("\r\n");
            diagnostics.append("OS version: ").append(System.getProperty("os.version")).append("\r\n");
            diagnostics.append("Manufacturer: ").append(Build.MANUFACTURER).append("\r\n");
            diagnostics.append("Model: ").append(Build.MODEL).append("\r\n");
            diagnostics.append("Product: ").append(Build.PRODUCT).append("\r\n");
            diagnostics.append("Brand: ").append(Build.BRAND).append("\r\n");


            intent.putExtra(Intent.EXTRA_TEXT, diagnostics.toString());
            File root = Files.storageFolder(getActivity());
            File file = new File(root, "/debuglog.txt");
            if (file.exists() && file.canRead()) {
                Uri uri = Uri.parse("file://" + file);
                intent.putExtra(Intent.EXTRA_STREAM, uri);
                startActivity(Intent.createChooser(intent, "Send debug log"));
            }
            else {
                Toast.makeText(getActivity(), "debuglog.txt not found", Toast.LENGTH_LONG).show();
            }

            return true;

        }

        if(preference.getKey().equals("audio_feedback_custom_file")){
            // Launch file picker for custom audio file
            try {
                Intent intent = new Intent(Intent.ACTION_GET_CONTENT);
                intent.setType("audio/*");
                intent.addCategory(Intent.CATEGORY_OPENABLE);
                intent.putExtra(Intent.EXTRA_LOCAL_ONLY, true);
                startActivityForResult(Intent.createChooser(intent, "选择音频文件"), REQUEST_AUDIO_FILE);
            } catch (Exception e) {
                Toast.makeText(getActivity(), "无法打开文件选择器", Toast.LENGTH_SHORT).show();
                LOG.error("Failed to open file picker", e);
            }
            return true;
        }

        if(preference.getKey().equals("audio_feedback_test")){
            // Test current audio feedback setting
            try {
                com.mendhak.gpslogger.ui.components.AudioFeedbackManager audioManager =
                    new com.mendhak.gpslogger.ui.components.AudioFeedbackManager(getContext());
                audioManager.testAudioFeedback();
                Toast.makeText(getActivity(), "播放测试音效", Toast.LENGTH_SHORT).show();
            } catch (Exception e) {
                Toast.makeText(getActivity(), "播放音效失败", Toast.LENGTH_SHORT).show();
                LOG.error("Failed to test audio feedback", e);
            }
            return true;
        }

        return false;
    }

    @Override
    public boolean onPreferenceChange(Preference preference, Object newValue) {

        if(preference.getKey().equals("changelanguage")){
            PreferenceHelper.getInstance().setUserSpecifiedLocale((String) newValue);
            LOG.debug("Language chosen: " + PreferenceHelper.getInstance().getUserSpecifiedLocale());
            return true;
        }
        if(preference.getKey().equals("coordinatedisplayformat")){
            PreferenceHelper.getInstance().setDisplayLatLongFormat(PreferenceNames.DegreesDisplayFormat.valueOf(newValue.toString()));
            LOG.debug("Coordinate format chosen: " + PreferenceHelper.getInstance().getDisplayLatLongFormat());
            setCoordinatesFormatPreferenceItem();
            return true;
        }
        if(preference.getKey().equalsIgnoreCase(PreferenceNames.APP_THEME_SETTING)){
            Dialogs.alert("", getString(R.string.restart_required), getActivity());
            return true;
        }
        if(preference.getKey().equalsIgnoreCase(PreferenceNames.ANNOTATIONS_BUTTON_COUNT)){
            int buttonCount = (Integer) newValue;
            PreferenceHelper.getInstance().setAnnotationButtonCount(buttonCount);
            LOG.debug("Annotation button count set to: " + buttonCount);

            // Notify annotation view about the change
            EventBus.getDefault().post(new AnnotationEvents.ButtonCountChanged(buttonCount));
            return true;
        }
        if(preference.getKey().equalsIgnoreCase(PreferenceNames.HAPTIC_FEEDBACK_ENABLED)){
            boolean enabled = (Boolean) newValue;
            PreferenceHelper.getInstance().setHapticFeedbackEnabled(enabled);
            LOG.debug("Haptic feedback enabled set to: " + enabled);
            return true;
        }
        if(preference.getKey().equalsIgnoreCase(PreferenceNames.HAPTIC_FEEDBACK_INTENSITY)){
            String intensity = (String) newValue;
            PreferenceHelper.getInstance().setHapticFeedbackIntensity(intensity);
            LOG.debug("Haptic feedback intensity set to: " + intensity);
            return true;
        }

        // Audio feedback settings
        if(preference.getKey().equalsIgnoreCase(PreferenceNames.AUDIO_FEEDBACK_TYPE)){
            String audioType = (String) newValue;
            PreferenceHelper.getInstance().setString(PreferenceNames.AUDIO_FEEDBACK_TYPE, audioType);
            LOG.debug("Audio feedback type set to: " + audioType);
            return true;
        }
        if(preference.getKey().equalsIgnoreCase(PreferenceNames.AUDIO_FEEDBACK_VOICE_ENABLED)){
            boolean enabled = (Boolean) newValue;
            PreferenceHelper.getInstance().setBoolean(PreferenceNames.AUDIO_FEEDBACK_VOICE_ENABLED, enabled);
            LOG.debug("Audio feedback for voice buttons set to: " + enabled);
            return true;
        }
        if(preference.getKey().equalsIgnoreCase(PreferenceNames.AUDIO_FEEDBACK_TEXT_ENABLED)){
            boolean enabled = (Boolean) newValue;
            PreferenceHelper.getInstance().setBoolean(PreferenceNames.AUDIO_FEEDBACK_TEXT_ENABLED, enabled);
            LOG.debug("Audio feedback for text buttons set to: " + enabled);
            return true;
        }
        if(preference.getKey().equalsIgnoreCase(PreferenceNames.AUDIO_FEEDBACK_COUNTER_ENABLED)){
            boolean enabled = (Boolean) newValue;
            PreferenceHelper.getInstance().setBoolean(PreferenceNames.AUDIO_FEEDBACK_COUNTER_ENABLED, enabled);
            LOG.debug("Audio feedback for counter buttons set to: " + enabled);
            return true;
        }

        // TTS feedback settings
        if(preference.getKey().equalsIgnoreCase(PreferenceNames.TTS_FEEDBACK_VOICE_ENABLED)){
            boolean enabled = (Boolean) newValue;
            PreferenceHelper.getInstance().setBoolean(PreferenceNames.TTS_FEEDBACK_VOICE_ENABLED, enabled);
            LOG.debug("TTS feedback for voice buttons set to: " + enabled);
            return true;
        }
        if(preference.getKey().equalsIgnoreCase(PreferenceNames.TTS_FEEDBACK_TEXT_ENABLED)){
            boolean enabled = (Boolean) newValue;
            PreferenceHelper.getInstance().setBoolean(PreferenceNames.TTS_FEEDBACK_TEXT_ENABLED, enabled);
            LOG.debug("TTS feedback for text buttons set to: " + enabled);
            return true;
        }
        if(preference.getKey().equalsIgnoreCase(PreferenceNames.TTS_FEEDBACK_COUNTER_ENABLED)){
            boolean enabled = (Boolean) newValue;
            PreferenceHelper.getInstance().setBoolean(PreferenceNames.TTS_FEEDBACK_COUNTER_ENABLED, enabled);
            LOG.debug("TTS feedback for counter buttons set to: " + enabled);
            return true;
        }

        // Handle annotation layout style changes
        if(preference.getKey().equalsIgnoreCase(PreferenceNames.ANNOTATION_LAYOUT_STYLE)){
            String layoutStyle = (String) newValue;
            PreferenceHelper.getInstance().setAnnotationLayoutStyle(layoutStyle);
            LOG.debug("Annotation layout style set to: " + layoutStyle);

            // Notify annotation view about the change
            EventBus.getDefault().post(new AnnotationEvents.LayoutStyleChanged(
                layoutStyle,
                PreferenceHelper.getInstance().getAnnotationSpacingMode(),
                PreferenceHelper.getInstance().getAnnotationViewMode()
            ));
            return true;
        }

        if(preference.getKey().equalsIgnoreCase(PreferenceNames.ANNOTATION_SPACING_MODE)){
            String spacingMode = (String) newValue;
            PreferenceHelper.getInstance().setAnnotationSpacingMode(spacingMode);
            LOG.debug("Annotation spacing mode set to: " + spacingMode);

            // Notify annotation view about the change
            EventBus.getDefault().post(new AnnotationEvents.LayoutStyleChanged(
                PreferenceHelper.getInstance().getAnnotationLayoutStyle(),
                spacingMode,
                PreferenceHelper.getInstance().getAnnotationViewMode()
            ));
            return true;
        }

        if(preference.getKey().equalsIgnoreCase(PreferenceNames.ANNOTATION_CUSTOM_SPACING)){
            int customSpacing = (Integer) newValue;
            PreferenceHelper.getInstance().setAnnotationCustomSpacing(customSpacing);
            LOG.debug("Annotation custom spacing set to: " + customSpacing);

            // Notify annotation view about the change
            EventBus.getDefault().post(new AnnotationEvents.LayoutStyleChanged(
                PreferenceHelper.getInstance().getAnnotationLayoutStyle(),
                PreferenceHelper.getInstance().getAnnotationSpacingMode(),
                PreferenceHelper.getInstance().getAnnotationViewMode()
            ));
            return true;
        }

        if(preference.getKey().equalsIgnoreCase(PreferenceNames.ANNOTATION_VIEW_MODE)){
            String viewMode = (String) newValue;
            PreferenceHelper.getInstance().setAnnotationViewMode(viewMode);
            LOG.debug("Annotation view mode set to: " + viewMode);

            // Notify annotation view about the change
            EventBus.getDefault().post(new AnnotationEvents.LayoutStyleChanged(
                PreferenceHelper.getInstance().getAnnotationLayoutStyle(),
                PreferenceHelper.getInstance().getAnnotationSpacingMode(),
                viewMode
            ));
            return true;
        }

        if(preference.getKey().equalsIgnoreCase(PreferenceNames.VOICE_INPUT_ENABLED)){
            boolean enabled = (Boolean) newValue;
            preferenceHelper.setVoiceInputEnabled(enabled);
            LOG.debug("Voice input enabled set to: " + enabled);
            return true;
        }

        return false;
    }

    @Override
    public boolean onResult(@NonNull String dialogTag, int which, @NonNull Bundle extras) {
        if(dialogTag.equalsIgnoreCase("RESET_APP") && which == BUTTON_POSITIVE){
            if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.KITKAT){
                    ((ActivityManager) getActivity().getSystemService(Context.ACTIVITY_SERVICE)).clearApplicationUserData();
            }
        }

        if(dialogTag.equalsIgnoreCase(PreferenceNames.VOICE_INPUT_LANGUAGE) && which == BUTTON_POSITIVE){
            String language = extras.getString(PreferenceNames.VOICE_INPUT_LANGUAGE);
            preferenceHelper.setVoiceInputLanguage(language);
            setPreferenceVoiceInputSummaries();
            return true;
        }

        if(dialogTag.equalsIgnoreCase(PreferenceNames.VOICE_INPUT_TIMEOUT) && which == BUTTON_POSITIVE){
            String timeoutStr = extras.getString(PreferenceNames.VOICE_INPUT_TIMEOUT);
            try {
                int timeout = Integer.parseInt(timeoutStr);
                if (timeout > 0 && timeout <= 60) {
                    preferenceHelper.setVoiceInputTimeout(timeout);
                    setPreferenceVoiceInputSummaries();
                } else {
                    android.widget.Toast.makeText(getActivity(), "超时时间必须在1-60秒之间", android.widget.Toast.LENGTH_SHORT).show();
                }
            } catch (NumberFormatException e) {
                android.widget.Toast.makeText(getActivity(), "请输入有效的数字", android.widget.Toast.LENGTH_SHORT).show();
            }
            return true;
        }

        return false;
    }

    @Override
    public void onActivityResult(int requestCode, int resultCode, Intent data) {
        super.onActivityResult(requestCode, resultCode, data);

        if (requestCode == REQUEST_AUDIO_FILE && resultCode == getActivity().RESULT_OK) {
            if (data != null && data.getData() != null) {
                android.net.Uri audioUri = data.getData();
                String audioPath = audioUri.toString();

                // Save the custom audio path
                preferenceHelper.setString(PreferenceNames.AUDIO_FEEDBACK_CUSTOM_PATH, audioPath);

                // Update the preference summary to show selected file
                Preference customFilePref = findPreference("audio_feedback_custom_file");
                if (customFilePref != null) {
                    String fileName = getFileNameFromUri(audioUri);
                    customFilePref.setSummary("已选择: " + fileName);
                }

                // Automatically switch to custom audio type
                preferenceHelper.setString(PreferenceNames.AUDIO_FEEDBACK_TYPE, "CUSTOM");

                Toast.makeText(getActivity(), "自定义音频文件已设置: " + getFileNameFromUri(audioUri),
                              Toast.LENGTH_SHORT).show();

                LOG.info("Custom audio file selected: {}", audioPath);
            }
        }
    }

    /**
     * Get file name from URI
     */
    private String getFileNameFromUri(android.net.Uri uri) {
        String fileName = "未知文件";
        try {
            if (uri.getScheme().equals("content")) {
                android.database.Cursor cursor = getActivity().getContentResolver().query(
                    uri, null, null, null, null);
                if (cursor != null) {
                    try {
                        if (cursor.moveToFirst()) {
                            int nameIndex = cursor.getColumnIndex(android.provider.OpenableColumns.DISPLAY_NAME);
                            if (nameIndex >= 0) {
                                fileName = cursor.getString(nameIndex);
                            }
                        }
                    } finally {
                        cursor.close();
                    }
                }
            } else {
                fileName = new java.io.File(uri.getPath()).getName();
            }
        } catch (Exception e) {
            LOG.warn("Failed to get file name from URI", e);
        }
        return fileName != null ? fileName : "未知文件";
    }
}
