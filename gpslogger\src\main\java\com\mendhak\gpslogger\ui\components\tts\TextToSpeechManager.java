/*
 * Copyright (C) 2016 mendhak
 *
 * This file is part of GPSLogger for Android.
 *
 * GPSLogger for Android is free software: you can redistribute it and/or modify
 * it under the terms of the GNU General Public License as published by
 * the Free Software Foundation, either version 2 of the License, or
 * (at your option) any later version.
 *
 * GPSLogger for Android is distributed in the hope that it will be useful,
 * but WITHOUT ANY WARRANTY; without even the implied warranty of
 * MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE.  See the
 * GNU General Public License for more details.
 *
 * You should have received a copy of the GNU General Public License
 * along with GPSLogger for Android.  If not, see <http://www.gnu.org/licenses/>.
 */

package com.mendhak.gpslogger.ui.components.tts;

import android.content.Context;
import android.os.Build;
import android.speech.tts.TextToSpeech;
import android.speech.tts.UtteranceProgressListener;

import com.mendhak.gpslogger.common.PreferenceHelper;
import com.mendhak.gpslogger.common.PreferenceNames;
import com.mendhak.gpslogger.common.slf4j.Logs;
import com.mendhak.gpslogger.ui.components.AudioFeedbackManager;
import com.mendhak.gpslogger.ui.components.LayoutEnums.TriggerMode;

import org.slf4j.Logger;

import java.util.HashMap;
import java.util.Locale;
import java.util.Map;

/**
 * Manager for Text-to-Speech functionality for annotation buttons
 * Provides TTS feedback when annotation buttons are clicked
 */
public class TextToSpeechManager implements TextToSpeech.OnInitListener {
    
    private static final Logger LOG = Logs.of(TextToSpeechManager.class);
    
    private Context context;
    private PreferenceHelper preferenceHelper;
    private TextToSpeech textToSpeech;
    private boolean isInitialized = false;
    private boolean isInitializing = false;
    
    /**
     * Button modes that can have TTS feedback
     */
    public enum ButtonMode {
        VOICE_INPUT("语音模式"),
        TEXT_INPUT("文本模式"),
        COUNTER_ONLY("计数器模式");
        
        private final String displayName;
        
        ButtonMode(String displayName) {
            this.displayName = displayName;
        }
        
        public String getDisplayName() {
            return displayName;
        }
        
        public static ButtonMode fromTriggerMode(TriggerMode triggerMode) {
            switch (triggerMode) {
                case VOICE_INPUT: return VOICE_INPUT;
                case TEXT_INPUT: return TEXT_INPUT;
                case COUNTER_ONLY: return COUNTER_ONLY;
                default: return VOICE_INPUT;
            }
        }
    }
    
    /**
     * Constructor
     */
    public TextToSpeechManager(Context context) {
        this.context = context.getApplicationContext();
        this.preferenceHelper = PreferenceHelper.getInstance();
        initializeTTS();
    }
    
    /**
     * Initialize TextToSpeech engine
     */
    private void initializeTTS() {
        if (isInitializing || isInitialized) {
            LOG.debug("TTS already initializing or initialized, skipping");
            return;
        }

        try {
            isInitializing = true;
            LOG.info("Initializing TextToSpeech engine");

            // Always try to initialize TTS, let the system handle availability
            textToSpeech = new TextToSpeech(context, this);
            LOG.debug("TextToSpeech constructor called, waiting for onInit callback");
        } catch (Exception e) {
            LOG.error("Failed to initialize TextToSpeech", e);
            isInitializing = false;
        }
    }

    
    @Override
    public void onInit(int status) {
        isInitializing = false;
        LOG.info("TTS onInit called with status: {}", status);

        if (status == TextToSpeech.SUCCESS) {
            try {
                LOG.debug("TTS initialization successful, configuring language and settings");

                // Set language to Chinese (Simplified) by default, fallback to system default
                int result = textToSpeech.setLanguage(Locale.SIMPLIFIED_CHINESE);
                LOG.debug("Setting Chinese language result: {}", result);

                if (result == TextToSpeech.LANG_MISSING_DATA || result == TextToSpeech.LANG_NOT_SUPPORTED) {
                    LOG.warn("Chinese language not supported (result: {}), trying system default", result);
                    result = textToSpeech.setLanguage(Locale.getDefault());
                    LOG.debug("Setting system default language result: {}", result);

                    if (result == TextToSpeech.LANG_MISSING_DATA || result == TextToSpeech.LANG_NOT_SUPPORTED) {
                        LOG.warn("System default language not supported (result: {}), using English", result);
                        result = textToSpeech.setLanguage(Locale.ENGLISH);
                        LOG.debug("Setting English language result: {}", result);
                    }
                }

                // Set speech rate and pitch
                textToSpeech.setSpeechRate(1.0f);
                textToSpeech.setPitch(1.0f);
                LOG.debug("TTS speech rate and pitch configured");

                // Set utterance progress listener for debugging
                if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.ICE_CREAM_SANDWICH_MR1) {
                    textToSpeech.setOnUtteranceProgressListener(new UtteranceProgressListener() {
                        @Override
                        public void onStart(String utteranceId) {
                            LOG.info("TTS started speaking: {}", utteranceId);
                        }

                        @Override
                        public void onDone(String utteranceId) {
                            LOG.info("TTS finished speaking: {}", utteranceId);
                        }

                        @Override
                        public void onError(String utteranceId) {
                            LOG.error("TTS error for utterance: {}", utteranceId);
                        }
                    });
                    LOG.debug("TTS utterance progress listener configured");
                }

                isInitialized = true;
                LOG.info("TextToSpeech initialized successfully and ready to use");

            } catch (Exception e) {
                LOG.error("Error configuring TextToSpeech", e);
                isInitialized = false;
            }
        } else {
            LOG.error("TextToSpeech initialization failed with status: {}", status);
            isInitialized = false;
        }
    }
    
    /**
     * Check if TTS feedback is enabled for the given button mode
     */
    public boolean isTTSFeedbackEnabled(ButtonMode buttonMode) {
        if (!isInitialized) {
            LOG.debug("TTS not initialized, feedback disabled");
            return false;
        }

        boolean enabled;
        switch (buttonMode) {
            case VOICE_INPUT:
                enabled = preferenceHelper.getBoolean(PreferenceNames.TTS_FEEDBACK_VOICE_ENABLED, false);
                LOG.debug("TTS feedback for VOICE_INPUT: {}", enabled);
                return enabled;
            case TEXT_INPUT:
                enabled = preferenceHelper.getBoolean(PreferenceNames.TTS_FEEDBACK_TEXT_ENABLED, false);
                LOG.debug("TTS feedback for TEXT_INPUT: {}", enabled);
                return enabled;
            case COUNTER_ONLY:
                enabled = preferenceHelper.getBoolean(PreferenceNames.TTS_FEEDBACK_COUNTER_ENABLED, false);
                LOG.debug("TTS feedback for COUNTER_ONLY: {}", enabled);
                return enabled;
            default:
                LOG.debug("Unknown button mode: {}, feedback disabled", buttonMode);
                return false;
        }
    }
    
    /**
     * Speak the button text using TTS
     */
    public void speakButtonText(String buttonText, ButtonMode buttonMode) {
        LOG.info("TTS speakButtonText called: text='{}', mode={}", buttonText, buttonMode.getDisplayName());

        if (!isTTSFeedbackEnabled(buttonMode)) {
            LOG.debug("TTS feedback not enabled for mode: {}", buttonMode.getDisplayName());
            return;
        }

        if (!isInitialized) {
            LOG.warn("TTS not initialized, attempting to reinitialize");
            reinitialize();
            return;
        }

        if (buttonText == null || buttonText.trim().isEmpty()) {
            LOG.debug("Button text is empty, skipping TTS");
            return;
        }

        // Check if TTS is currently speaking and stop it to avoid overlap
        if (isSpeaking()) {
            LOG.debug("TTS is currently speaking, stopping current speech");
            stopSpeaking();
        }

        try {
            String textToSpeak = preprocessText(buttonText.trim());
            String utteranceId = "button_" + System.currentTimeMillis();

            LOG.info("TTS attempting to speak: '{}' with utteranceId: {}", textToSpeak, utteranceId);

            if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.LOLLIPOP) {
                // Android 5.0+ (API 21+) - Use new speak method
                android.os.Bundle params = new android.os.Bundle();
                params.putFloat(TextToSpeech.Engine.KEY_PARAM_VOLUME, 1.0f);
                int result = textToSpeech.speak(textToSpeak, TextToSpeech.QUEUE_FLUSH, params, utteranceId);
                LOG.debug("TTS speak method result: {}", result);
            } else {
                // Android 4.4 and below - Use legacy speak method
                HashMap<String, String> params = new HashMap<>();
                params.put(TextToSpeech.Engine.KEY_PARAM_UTTERANCE_ID, utteranceId);
                params.put(TextToSpeech.Engine.KEY_PARAM_VOLUME, "1.0");
                int result = textToSpeech.speak(textToSpeak, TextToSpeech.QUEUE_FLUSH, params);
                LOG.debug("TTS speak method (legacy) result: {}", result);
            }

            LOG.info("TTS speak command sent successfully for text: '{}', mode: {}", textToSpeak, buttonMode.getDisplayName());

        } catch (Exception e) {
            LOG.error("Failed to speak button text: {}", buttonText, e);
            // Try to reinitialize TTS if speaking fails
            if (isInitialized) {
                LOG.debug("Attempting to reinitialize TTS after speak failure");
                shutdown();
                reinitialize();
            }
        }
    }

    /**
     * Preprocess text for better TTS pronunciation
     */
    private String preprocessText(String text) {
        if (text == null) return "";

        // Remove special characters that might cause TTS issues
        String processed = text.replaceAll("[\\[\\]{}()\"'`~!@#$%^&*+=|\\\\:;\"'<>,.?/]", " ");

        // Replace multiple spaces with single space
        processed = processed.replaceAll("\\s+", " ").trim();

        // If text is too long, truncate it
        if (processed.length() > 100) {
            processed = processed.substring(0, 97) + "...";
        }

        return processed;
    }
    
    /**
     * Stop current TTS speech
     */
    public void stopSpeaking() {
        if (isInitialized && textToSpeech != null) {
            try {
                textToSpeech.stop();
                LOG.debug("TTS speech stopped");
            } catch (Exception e) {
                LOG.warn("Error stopping TTS speech", e);
            }
        }
    }
    
    /**
     * Check if TTS is currently speaking
     */
    public boolean isSpeaking() {
        if (isInitialized && textToSpeech != null) {
            try {
                return textToSpeech.isSpeaking();
            } catch (Exception e) {
                LOG.warn("Error checking TTS speaking status", e);
                return false;
            }
        }
        return false;
    }
    
    /**
     * Release TTS resources
     */
    public void shutdown() {
        if (textToSpeech != null) {
            try {
                textToSpeech.stop();
                textToSpeech.shutdown();
                LOG.debug("TextToSpeech shutdown completed");
            } catch (Exception e) {
                LOG.warn("Error shutting down TextToSpeech", e);
            } finally {
                textToSpeech = null;
                isInitialized = false;
                isInitializing = false;
            }
        }
    }
    
    /**
     * Check if TTS is available and initialized
     */
    public boolean isAvailable() {
        return isInitialized && textToSpeech != null;
    }
    
    /**
     * Reinitialize TTS if needed
     */
    public void reinitialize() {
        if (!isInitialized && !isInitializing) {
            LOG.debug("Reinitializing TTS");
            initializeTTS();
        }
    }

    /**
     * Check TTS health and reinitialize if necessary
     */
    public void checkAndMaintainTTS() {
        if (!isInitialized || textToSpeech == null) {
            LOG.debug("TTS not initialized, attempting initialization");
            reinitialize();
            return;
        }

        try {
            // Try to get the current language to test if TTS is responsive
            java.util.Locale currentLocale = textToSpeech.getLanguage();
            if (currentLocale == null) {
                LOG.warn("TTS language is null, reinitializing");
                shutdown();
                reinitialize();
            }
        } catch (Exception e) {
            LOG.warn("TTS health check failed, reinitializing", e);
            shutdown();
            reinitialize();
        }
    }
}
